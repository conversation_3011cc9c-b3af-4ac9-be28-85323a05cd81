absl-py==2.1.0
adal==1.2.7
aiohappyeyeballs==2.4.6
aiohttp==3.11.12
aiosignal==1.3.2
albucore==0.0.23
albumentations==2.0.4
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
argcomplete==3.5.3
arrow==1.3.0
asttokens==3.0.0
async-timeout==5.0.1
azure-common==1.1.28
azure-core==1.32.0
azure-graphrbac==0.61.2
azure-mgmt-authorization==4.0.0
azure-mgmt-containerregistry==10.3.0
azure-mgmt-core==1.5.0
azure-mgmt-keyvault==10.3.1
azure-mgmt-network==28.1.0
azure-mgmt-resource==23.2.0
azure-mgmt-storage==22.0.0
azureml==0.2.7
azureml-core==1.59.0.post1
bcrypt==4.2.1
blessed==1.20.0
CATCH==0.0
certifi==2025.1.31
charset-normalizer==3.4.1
comm==0.2.2
contextlib2==21.6.0
contourpy==1.3.1
cycler==0.12.1
debugpy==1.8.13
decorator==5.2.1
Deprecated==1.2.18
dgl==1.1.3+cu121
docker==7.1.0
efficientnet_pytorch==0.7.1
et_xmlfile==2.0.0
exceptiongroup==1.2.2
executing==2.1.0
filelock==3.17.0
fonttools==4.56.0
fqdn==1.5.1
frozenlist==1.5.0
fsspec==2025.2.0
fvcore==0.1.5.post20221221
gpustat==1.1.1
graphtools==1.5.3
grpcio==1.70.0
huggingface-hub==0.29.3
humanfriendly==10.0
idna==3.10
imagecodecs==2025.3.30
imageio==2.37.0
importlib_metadata==8.5.0
importlib_resources==6.5.2
iopath==0.1.10
ipykernel==6.29.5
ipympl==0.9.6
ipython==8.34.0
ipywidgets==8.1.5
isodate==0.7.2
isoduration==20.11.0
jedi==0.19.2
Jinja2==3.1.6
jmespath==1.0.1
joblib==1.4.2
jsonpickle==4.0.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyterlab_widgets==3.0.13
kiwisolver==1.4.8
knack==0.12.0
lazy_loader==0.4
lightning-utilities==0.12.0
Markdown==3.7
MarkupSafe==3.0.2
matplotlib==3.10.1
matplotlib-inline==0.1.7
MedPy==0.5.2
medsam==0.0.1
monai==1.4.0
mpmath==1.3.0
msal==1.31.1
msal-extensions==1.2.0
msrest==0.7.1
msrestazure==0.6.4.post1
multidict==6.1.0
multiscale-phate==0.0
munch==4.0.0
natsort==8.4.0
ndg-httpsclient==0.5.1
nest_asyncio==1.6.0
networkx==3.4.2
nibabel==5.3.2
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-ml-py==12.570.86
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
oauthlib==3.2.2
omegaconf==2.3.0
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
packaging==24.2
pandas==2.0.3
paramiko==3.5.1
parso==0.8.4
pathspec==0.12.1
pexpect==4.9.0
phate==1.0.11
pickleshare==0.7.5
pillow==11.1.0
pip==25.0
platformdirs==4.3.6
portalocker==2.10.1
pretrainedmodels==0.7.4
prompt_toolkit==3.0.50
propcache==0.2.1
protobuf==5.29.3
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pydantic==2.10.6
pydantic_core==2.27.2
Pygments==2.19.1
PyGSP==0.5.1
PyNaCl==1.5.0
pyparsing==3.2.1
python-dateutil==2.9.0.post0
pytorch-lightning==1.9.0
pytz==2025.1
PyYAML==6.0.2
pyzmq==26.3.0
requests==2.32.3
requests-oauthlib==2.0.0
s-gd2==1.8.1
safetensors==0.5.3
scikit-image==0.25.1
scikit-learn==1.6.1
scipy==1.15.2
scprep==1.2.3
segment-anything==1.0
segmentation_models_pytorch==0.4.0
setuptools==75.8.0
sewar==0.4.6
SimpleITK==2.4.1
simsimd==6.2.1
six==1.17.0
stack_data==0.6.3
stringzilla==3.11.3
sympy==1.13.1
tabulate==0.9.0
tasklogger==1.2.0
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorboardX==*******
termcolor==3.0.1
threadpoolctl==3.5.0
tifffile==2025.1.10
timm==1.0.15
torch==2.6.0
torchdata==0.11.0
torchdiffeq==0.2.5
torchinfo==1.8.0
torchmetrics==1.6.1
torchsummary==1.5.1
torchvision==0.21.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
triton==3.2.0
types-dataclasses==0.6.6
types-python-dateutil==2.9.0.20241206
types-PyYAML==6.0.12.20241230
types-requests==2.32.0.20241016
typing_extensions==4.12.2
unzip==1.0.0
uri-template==1.3.0
urllib3==2.3.0
wcwidth==0.2.13
webcolors==24.11.1
Werkzeug==3.1.3
wget==3.2
wheel==0.45.1
widgetsnbextension==4.0.13
wrapt==1.17.2
yacs==0.1.8
yarl==1.18.3
zipp==3.21.0
