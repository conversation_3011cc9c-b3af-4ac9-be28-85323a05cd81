[data paths]
path_local =  _datasets_training_testing/
train_imgs_original = _imgs_train.hdf5
train_groundTruth = _groundTruth_train.hdf5
train_border_masks = _borderMasks_train.hdf5
test_imgs_original = _imgs_test.hdf5
test_groundTruth = _groundTruth_test.hdf5
test_border_masks = _borderMasks_test.hdf5
model_path=./models/

[experiment name]
#name= UNet
#name= AttUNet
#name= DUNet
#name= UKAN
#name= DSCNet
#name= RollingUNet
#name= mambaUNet
#name= CTFNet
#name= BCDUNet
#name= IterNet
#name= UNet++
name=  AttUKAN

[data attributes]
#Dimensions of the patches extracted from the full images
patch_height = 64
patch_width = 64
channels = 3

dataset=DRIVE

[training settings]
#number of total patches:
N_subimgs = 20000
#if patches are extracted only inside the field of view:
inside_FOV = False
#Number of training epochs
N_epochs = 100
batch_size = 35
lr = 3e-3
#To use LPCL or not
CL_loss = True 


[testing settings]
N_group_visual = 1
#Compute average in the prediction, improve results but require more patches to be predicted
average_mode = True
stride_height = 5
stride_width = 5
#if running with nohup
nohup = False
